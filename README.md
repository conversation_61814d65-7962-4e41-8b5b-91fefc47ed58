# F# High-Performance Trading System

A professional-grade, event-driven trading system built in F# for backtesting and live trading.

## 🚀 Quick Start

### Prerequisites
- .NET 6.0+ SDK
- Linux/macOS/Windows
- 8GB+ RAM (for large datasets)

### Installation
```bash
# Clone and setup
git clone <your-repo>
cd f-sharp
chmod +x scripts/*.sh
./scripts/setup.sh
```

### First Backtest
```bash
# Build the system
./scripts/build.sh

# Run a simple EMA crossover strategy
dotnet run --project src/TradingSystem.CLI -- backtest \
  --strategy examples/ema-crossover.fs \
  --data ../4-strategy-tester/STRATEGY_DATA/strategy_data_EURUSD_2025-01-01_to_2025-07-18_1m.parquet \
  --timeframe 1m
```

## 📁 Project Structure

- `src/` - Source code organized by domain
- `tests/` - Unit and integration tests  
- `examples/` - Example strategies and configurations
- `docs/` - Documentation and guides
- `scripts/` - Build and deployment automation

## 🎯 Key Features

- **High Performance**: Process millions of events per second
- **Type Safety**: F#'s type system prevents runtime errors
- **Event-Driven**: True reactive architecture with backpressure
- **ML Integration**: Built-in optimization and strategy discovery
- **Live Trading**: Real-time AMQP integration with JForex
- **User Friendly**: Simple DSL for complex strategies

## 📖 Documentation

- [Architecture Overview](docs/architecture/README.md)
- [Strategy Development Guide](docs/strategies/README.md)
- [API Reference](docs/api/README.md)
- [Deployment Guide](docs/deployment/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
