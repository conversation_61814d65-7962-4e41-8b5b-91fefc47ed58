﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A46E7083-7637-45D1-89BC-A74C7847C11F}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.Core", "src\TradingSystem.Core\TradingSystem.Core.fsproj", "{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.Data", "src\TradingSystem.Data\TradingSystem.Data.fsproj", "{B78B9890-468A-44C5-BF7F-4E35A8E715C5}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.Strategy", "src\TradingSystem.Strategy\TradingSystem.Strategy.fsproj", "{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.Execution", "src\TradingSystem.Execution\TradingSystem.Execution.fsproj", "{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.ML", "src\TradingSystem.ML\TradingSystem.ML.fsproj", "{A1B7C315-9C2D-412D-96F4-3627C22809E2}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.CLI", "src\TradingSystem.CLI\TradingSystem.CLI.fsproj", "{95F33203-9E89-493E-A93A-0AEF2D733923}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{3C9B8A2D-6835-4492-99D4-9D725DCDDA80}"
EndProject
Project("{F2A71F9B-5D33-465A-A702-920D77279786}") = "TradingSystem.Tests", "tests\TradingSystem.Tests\TradingSystem.Tests.fsproj", "{32FB9383-8B1B-4305-91F7-969F9C5A9392}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056}.Release|Any CPU.Build.0 = Release|Any CPU
		{B78B9890-468A-44C5-BF7F-4E35A8E715C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B78B9890-468A-44C5-BF7F-4E35A8E715C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B78B9890-468A-44C5-BF7F-4E35A8E715C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B78B9890-468A-44C5-BF7F-4E35A8E715C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B}.Release|Any CPU.Build.0 = Release|Any CPU
		{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574}.Release|Any CPU.Build.0 = Release|Any CPU
		{A1B7C315-9C2D-412D-96F4-3627C22809E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B7C315-9C2D-412D-96F4-3627C22809E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B7C315-9C2D-412D-96F4-3627C22809E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B7C315-9C2D-412D-96F4-3627C22809E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{95F33203-9E89-493E-A93A-0AEF2D733923}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95F33203-9E89-493E-A93A-0AEF2D733923}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95F33203-9E89-493E-A93A-0AEF2D733923}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95F33203-9E89-493E-A93A-0AEF2D733923}.Release|Any CPU.Build.0 = Release|Any CPU
		{32FB9383-8B1B-4305-91F7-969F9C5A9392}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32FB9383-8B1B-4305-91F7-969F9C5A9392}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32FB9383-8B1B-4305-91F7-969F9C5A9392}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{32FB9383-8B1B-4305-91F7-969F9C5A9392}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5D4F9C96-ECA1-47A6-AEB4-61760DEF4056} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{B78B9890-468A-44C5-BF7F-4E35A8E715C5} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{326D5014-DACE-4FA1-BA6C-2F4188AFDF1B} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{94CDBE20-E8C2-4BA9-8A3A-E8400BA69574} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{A1B7C315-9C2D-412D-96F4-3627C22809E2} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{95F33203-9E89-493E-A93A-0AEF2D733923} = {A46E7083-7637-45D1-89BC-A74C7847C11F}
		{32FB9383-8B1B-4305-91F7-969F9C5A9392} = {3C9B8A2D-6835-4492-99D4-9D725DCDDA80}
	EndGlobalSection
EndGlobal
