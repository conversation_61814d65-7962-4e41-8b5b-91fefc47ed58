#!/bin/bash

# F# Trading System - Development Environment Setup Script
# This script sets up the complete development environment on Linux

set -e  # Exit on any error

echo "🚀 Setting up F# Trading System Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Linux
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    print_warning "This script is optimized for Linux. Some steps may need manual adjustment."
fi

# Check if .NET is installed
print_status "Checking .NET installation..."
if ! command -v dotnet &> /dev/null; then
    print_error ".NET SDK not found. Please install .NET 6.0+ first:"
    echo "  wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb"
    echo "  sudo dpkg -i packages-microsoft-prod.deb"
    echo "  sudo apt-get update"
    echo "  sudo apt-get install -y dotnet-sdk-6.0"
    exit 1
fi

# Check .NET version
DOTNET_VERSION=$(dotnet --version)
print_success ".NET SDK version: $DOTNET_VERSION"

# Install global tools
print_status "Installing F# development tools..."
dotnet tool install -g fantomas-tool --version 4.7.9 || print_warning "Fantomas already installed"
dotnet tool install -g paket --version 7.2.1 || print_warning "Paket already installed"
dotnet tool install -g fake-cli --version 5.23.1 || print_warning "FAKE already installed"

# Create directory structure
print_status "Creating project directory structure..."
mkdir -p src/{TradingSystem.Core,TradingSystem.Data,TradingSystem.Strategy,TradingSystem.Execution,TradingSystem.ML,TradingSystem.Web,TradingSystem.CLI}
mkdir -p tests/{TradingSystem.Tests,TradingSystem.IntegrationTests}
mkdir -p data/{parquet,config}
mkdir -p docs/{architecture,strategies,api}
mkdir -p examples

# Create solution file
print_status "Creating solution file..."
if [ ! -f "TradingSystem.sln" ]; then
    dotnet new sln -n TradingSystem
    print_success "Created solution file"
else
    print_warning "Solution file already exists"
fi

# Create core projects
print_status "Creating F# projects..."

# Function to create project safely
create_project() {
    local project_dir=$1
    local project_name=$2
    local template=$3

    if [ ! -f "${project_dir}/${project_name}.fsproj" ]; then
        # Remove any existing files first
        rm -rf "$project_dir"
        mkdir -p "$project_dir"
        cd "$project_dir"
        dotnet new "$template" -lang F# -n "$project_name" --force
        # Move files from nested directory if created
        if [ -d "$project_name" ]; then
            mv "$project_name"/* .
            rmdir "$project_name"
        fi
        cd - > /dev/null
        dotnet sln add "${project_dir}/${project_name}.fsproj"
        print_success "Created $project_name project"
    else
        print_warning "$project_name project already exists"
    fi
}

# Core domain project
create_project "src/TradingSystem.Core" "TradingSystem.Core" "classlib"

# Data access project
create_project "src/TradingSystem.Data" "TradingSystem.Data" "classlib"

# Strategy framework project
create_project "src/TradingSystem.Strategy" "TradingSystem.Strategy" "classlib"

# Execution project
create_project "src/TradingSystem.Execution" "TradingSystem.Execution" "classlib"

# ML project
create_project "src/TradingSystem.ML" "TradingSystem.ML" "classlib"

# CLI project
create_project "src/TradingSystem.CLI" "TradingSystem.CLI" "console"

# Test project
create_project "tests/TradingSystem.Tests" "TradingSystem.Tests" "xunit"

# Create symlink to existing parquet data
print_status "Creating symlink to existing parquet data..."
if [ -d "../4-strategy-tester/STRATEGY_DATA" ]; then
    if [ ! -L "data/parquet/strategy_data" ]; then
        ln -s "$(pwd)/../4-strategy-tester/STRATEGY_DATA" data/parquet/strategy_data
        print_success "Created symlink to strategy data"
    else
        print_warning "Symlink to strategy data already exists"
    fi
else
    print_warning "Strategy data directory not found at ../4-strategy-tester/STRATEGY_DATA"
fi

# Add project references
print_status "Adding project references..."
dotnet add src/TradingSystem.Data/TradingSystem.Data.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj
dotnet add src/TradingSystem.Strategy/TradingSystem.Strategy.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj
dotnet add src/TradingSystem.Execution/TradingSystem.Execution.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj
dotnet add src/TradingSystem.Execution/TradingSystem.Execution.fsproj reference src/TradingSystem.Strategy/TradingSystem.Strategy.fsproj
dotnet add src/TradingSystem.ML/TradingSystem.ML.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj
dotnet add src/TradingSystem.CLI/TradingSystem.CLI.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj
dotnet add src/TradingSystem.CLI/TradingSystem.CLI.fsproj reference src/TradingSystem.Data/TradingSystem.Data.fsproj
dotnet add src/TradingSystem.CLI/TradingSystem.CLI.fsproj reference src/TradingSystem.Strategy/TradingSystem.Strategy.fsproj
dotnet add src/TradingSystem.CLI/TradingSystem.CLI.fsproj reference src/TradingSystem.Execution/TradingSystem.Execution.fsproj
dotnet add tests/TradingSystem.Tests/TradingSystem.Tests.fsproj reference src/TradingSystem.Core/TradingSystem.Core.fsproj

# Install NuGet packages
print_status "Installing NuGet packages..."
dotnet restore

# Create basic configuration files
print_status "Creating configuration files..."

# Create .gitignore
if [ ! -f ".gitignore" ]; then
    cat > .gitignore << 'EOF'
## F# and .NET
bin/
obj/
*.user
*.suo
*.cache
*.docstates
*.tmp
*.log

## IDE
.vs/
.vscode/
.idea/
*.swp
*.swo

## OS
.DS_Store
Thumbs.db

## Data
data/parquet/*.parquet
data/cache/
logs/
EOF
    print_success "Created .gitignore"
fi

# Create development configuration
if [ ! -f "data/config/development.json" ]; then
    cat > data/config/development.json << 'EOF'
{
  "DataSources": {
    "ParquetPath": "data/parquet/strategy_data",
    "CacheEnabled": true,
    "CachePath": "data/cache"
  },
  "Trading": {
    "DefaultRiskPerTrade": 0.02,
    "MaxConcurrentTrades": 5,
    "SlippageModel": "Linear"
  },
  "AMQP": {
    "ConnectionString": "amqp://localhost:5672",
    "Exchange": "trading.events",
    "Queues": {
      "MarketData": "market.data",
      "Orders": "orders",
      "Executions": "executions"
    }
  },
  "Logging": {
    "Level": "Information",
    "File": "logs/trading-system.log"
  }
}
EOF
    print_success "Created development configuration"
fi

# Make scripts executable
chmod +x scripts/*.sh

print_success "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Run './scripts/build.sh' to build all projects"
echo "2. Check out the examples in the 'examples/' directory"
echo "3. Read the documentation in 'docs/'"
echo "4. Start developing your first strategy!"
echo ""
print_status "Happy trading! 📈"
