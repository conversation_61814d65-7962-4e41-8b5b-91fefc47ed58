#!/bin/bash

# F# Trading System - Build Script
# Builds all projects with optimizations and runs tests

set -e  # Exit on any error

echo "🔨 Building F# Trading System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[BUILD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
BUILD_CONFIG="Release"
RUN_TESTS=true
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_CONFIG="Debug"
            shift
            ;;
        --no-tests)
            RUN_TESTS=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 [--debug] [--no-tests] [--verbose]"
            exit 1
            ;;
    esac
done

print_status "Build configuration: $BUILD_CONFIG"

# Clean previous builds
print_status "Cleaning previous builds..."
dotnet clean --configuration $BUILD_CONFIG --verbosity quiet
if [ $? -eq 0 ]; then
    print_success "Clean completed"
else
    print_error "Clean failed"
    exit 1
fi

# Restore NuGet packages
print_status "Restoring NuGet packages..."
if [ "$VERBOSE" = true ]; then
    dotnet restore --verbosity normal
else
    dotnet restore --verbosity quiet
fi

if [ $? -eq 0 ]; then
    print_success "Package restore completed"
else
    print_error "Package restore failed"
    exit 1
fi

# Build solution
print_status "Building solution..."
if [ "$VERBOSE" = true ]; then
    dotnet build --configuration $BUILD_CONFIG --no-restore --verbosity normal
else
    dotnet build --configuration $BUILD_CONFIG --no-restore --verbosity quiet
fi

if [ $? -eq 0 ]; then
    print_success "Build completed successfully"
else
    print_error "Build failed"
    exit 1
fi

# Run tests if requested
if [ "$RUN_TESTS" = true ]; then
    print_status "Running tests..."
    
    if [ "$VERBOSE" = true ]; then
        dotnet test --configuration $BUILD_CONFIG --no-build --verbosity normal
    else
        dotnet test --configuration $BUILD_CONFIG --no-build --verbosity quiet
    fi
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Some tests failed"
        exit 1
    fi
fi

# Display build summary
echo ""
print_success "🎉 Build completed successfully!"
echo ""
echo "Build artifacts:"
echo "  • CLI: src/TradingSystem.CLI/bin/$BUILD_CONFIG/net6.0/"
echo "  • Libraries: src/*/bin/$BUILD_CONFIG/net6.0/"
echo ""
echo "Quick start:"
echo "  • Run CLI: dotnet run --project src/TradingSystem.CLI"
echo "  • Run tests: dotnet test"
echo "  • Format code: dotnet fantomas src/ tests/"
echo ""
print_status "Ready for trading! 📈"
