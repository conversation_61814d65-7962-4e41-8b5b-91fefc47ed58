{"DataSources": {"ParquetPath": "data/parquet/strategy_data", "CacheEnabled": true, "CachePath": "data/cache"}, "Trading": {"DefaultRiskPerTrade": 0.02, "MaxConcurrentTrades": 5, "SlippageModel": "Linear"}, "AMQP": {"ConnectionString": "amqp://localhost:5672", "Exchange": "trading.events", "Queues": {"MarketData": "market.data", "Orders": "orders", "Executions": "executions"}}, "Logging": {"Level": "Information", "File": "logs/trading-system.log"}}