// Example: EMA Crossover Strategy
// This demonstrates the F# trading system's strategy DSL

open TradingSystem.Core
open TradingSystem.Strategy

// =============================================================================
// STRATEGY DEFINITION
// =============================================================================

/// EMA Crossover Strategy Parameters
type EmaCrossoverParams = {
    FastPeriod: int
    SlowPeriod: int
    RsiPeriod: int
    RsiOverbought: decimal
    RsiOversold: decimal
    StopLossAtr: decimal
    TakeProfitAtr: decimal
    AtrPeriod: int
}

/// Strategy state to track crossover signals
type EmaCrossoverState = {
    LastFastEma: decimal option
    LastSlowEma: decimal option
    LastRsi: decimal option
    LastAtr: decimal option
    BarsInTrade: int
    MaxBarsInTrade: int
}

// Default parameters
let defaultParams = {
    FastPeriod = 20
    SlowPeriod = 50
    RsiPeriod = 14
    RsiOverbought = 70m
    RsiOversold = 30m
    StopLossAtr = 2.0m
    TakeProfitAtr = 3.0m
    AtrPeriod = 14
    MaxBarsInTrade = 100
}

let initialState = {
    LastFastEma = None
    LastSlowEma = None
    LastRsi = None
    LastAtr = None
    BarsInTrade = 0
    MaxBarsInTrade = defaultParams.MaxBarsInTrade
}

// =============================================================================
// STRATEGY LOGIC
// =============================================================================

/// Check for bullish crossover (fast EMA crosses above slow EMA)
let isBullishCrossover currentFast currentSlow lastFast lastSlow =
    match lastFast, lastSlow with
    | Some prevFast, Some prevSlow ->
        prevFast <= prevSlow && currentFast > currentSlow
    | _ -> false

/// Check for bearish crossover (fast EMA crosses below slow EMA)
let isBearishCrossover currentFast currentSlow lastFast lastSlow =
    match lastFast, lastSlow with
    | Some prevFast, Some prevSlow ->
        prevFast >= prevSlow && currentFast < currentSlow
    | _ -> false

/// Main strategy logic
let emaCrossoverStrategy (params: EmaCrossoverParams) (state: EmaCrossoverState) (event: MarketEvent) =
    match event with
    | Bar ohlc ->
        // Extract required indicators (these would come from the indicator engine)
        let fastEma = getIndicator $"EMA_{params.FastPeriod}" ohlc.Timestamp
        let slowEma = getIndicator $"EMA_{params.SlowPeriod}" ohlc.Timestamp  
        let rsi = getIndicator $"RSI_{params.RsiPeriod}" ohlc.Timestamp
        let atr = getIndicator $"ATR_{params.AtrPeriod}" ohlc.Timestamp
        
        match fastEma, slowEma, rsi, atr with
        | Some fast, Some slow, Some rsiValue, Some atrValue ->
            
            // Update state
            let newState = {
                state with
                    LastFastEma = Some fast
                    LastSlowEma = Some slow
                    LastRsi = Some rsiValue
                    LastAtr = Some atrValue
                    BarsInTrade = if hasOpenPosition() then state.BarsInTrade + 1 else 0
            }
            
            // Generate signals
            let signals = [
                // Entry signals
                if not (hasOpenPosition()) then
                    // Long entry: bullish crossover + RSI not overbought
                    if isBullishCrossover fast slow state.LastFastEma state.LastSlowEma && 
                       rsiValue < params.RsiOverbought then
                        yield EntrySignal (Long, $"EMA bullish crossover, RSI: {rsiValue}")
                    
                    // Short entry: bearish crossover + RSI not oversold
                    elif isBearishCrossover fast slow state.LastFastEma state.LastSlowEma && 
                         rsiValue > params.RsiOversold then
                        yield EntrySignal (Short, $"EMA bearish crossover, RSI: {rsiValue}")
                
                // Exit signals
                else
                    let currentPosition = getCurrentPosition()
                    match currentPosition with
                    | Some pos ->
                        // Exit long on bearish crossover
                        if pos.Direction = Long && 
                           isBearishCrossover fast slow state.LastFastEma state.LastSlowEma then
                            yield ExitSignal "Bearish crossover"
                        
                        // Exit short on bullish crossover  
                        elif pos.Direction = Short && 
                             isBullishCrossover fast slow state.LastFastEma state.LastSlowEma then
                            yield ExitSignal "Bullish crossover"
                        
                        // Exit on maximum bars in trade
                        elif newState.BarsInTrade >= newState.MaxBarsInTrade then
                            yield ExitSignal "Maximum bars in trade reached"
                    | None -> ()
            ]
            
            (newState, signals)
            
        | _ ->
            // Not all indicators available yet
            (state, [])
    
    | _ ->
        // Ignore other event types for this strategy
        (state, [])

// =============================================================================
// STRATEGY CONFIGURATION
// =============================================================================

/// Create the complete strategy configuration
let createEmaCrossoverStrategy params =
    {
        Id = Domain.newStrategyId "EmaCrossover"
        Name = "EMA Crossover Strategy"
        Description = "Trades EMA crossovers filtered by RSI"
        Version = "1.0"
        Author = "F# Trading System"
        
        // Required indicators
        RequiredIndicators = [
            $"EMA_{params.FastPeriod}"
            $"EMA_{params.SlowPeriod}" 
            $"RSI_{params.RsiPeriod}"
            $"ATR_{params.AtrPeriod}"
        ]
        
        // Strategy parameters
        Parameters = Map.ofList [
            ("FastPeriod", box params.FastPeriod)
            ("SlowPeriod", box params.SlowPeriod)
            ("RsiPeriod", box params.RsiPeriod)
            ("RsiOverbought", box params.RsiOverbought)
            ("RsiOversold", box params.RsiOversold)
            ("StopLossAtr", box params.StopLossAtr)
            ("TakeProfitAtr", box params.TakeProfitAtr)
            ("AtrPeriod", box params.AtrPeriod)
        ]
        
        // Risk management
        RiskRules = [
            MaxRiskPerTrade 0.02m // 2% risk per trade
            MaxConcurrentTrades 3
            MaxDrawdown 0.10m // 10% max drawdown
        ]
        
        // Strategy logic function
        OnEvent = emaCrossoverStrategy params
        InitialState = box initialState
        
        // Timeframes this strategy operates on
        Timeframes = [ Minutes 5; Minutes 15; Hours 1 ]
        
        // Instruments this strategy can trade
        Instruments = [ "EURUSD"; "GBPUSD"; "USDJPY" ]
    }

// =============================================================================
// USAGE EXAMPLES
// =============================================================================

// Create strategy with default parameters
let defaultStrategy = createEmaCrossoverStrategy defaultParams

// Create strategy with custom parameters
let aggressiveStrategy = 
    createEmaCrossoverStrategy {
        defaultParams with
            FastPeriod = 10
            SlowPeriod = 30
            StopLossAtr = 1.5m
            TakeProfitAtr = 4.0m
    }

// Create conservative strategy
let conservativeStrategy =
    createEmaCrossoverStrategy {
        defaultParams with
            FastPeriod = 50
            SlowPeriod = 200
            RsiOverbought = 65m
            RsiOversold = 35m
            StopLossAtr = 3.0m
            TakeProfitAtr = 2.0m
    }

// =============================================================================
// PARAMETER OPTIMIZATION SPACE
// =============================================================================

/// Define the parameter space for optimization
let optimizationSpace = [
    IntParameter ("FastPeriod", 5, 50, 5)
    IntParameter ("SlowPeriod", 20, 200, 10) 
    IntParameter ("RsiPeriod", 7, 21, 7)
    DecimalParameter ("RsiOverbought", 60m, 80m, 5m)
    DecimalParameter ("RsiOversold", 20m, 40m, 5m)
    DecimalParameter ("StopLossAtr", 1.0m, 4.0m, 0.5m)
    DecimalParameter ("TakeProfitAtr", 1.5m, 5.0m, 0.5m)
]

/// Constraint function to ensure fast period < slow period
let parameterConstraints (paramSet: Map<string, obj>) =
    let fastPeriod = paramSet.["FastPeriod"] :?> int
    let slowPeriod = paramSet.["SlowPeriod"] :?> int
    fastPeriod < slowPeriod

// Export the strategy for the trading system
let exportedStrategy = {
    Strategy = defaultStrategy
    OptimizationSpace = Some optimizationSpace
    ParameterConstraints = Some parameterConstraints
}
