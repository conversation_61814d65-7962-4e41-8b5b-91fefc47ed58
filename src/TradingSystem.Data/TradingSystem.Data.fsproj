﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Library.fs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Parquet.Net" Version="4.15.0" />
    <PackageReference Include="Microsoft.ML" Version="3.0.1" />
    <PackageReference Include="Microsoft.ML.AutoML" Version="0.21.1" />
    <PackageReference Include="Microsoft.ML.FastTree" Version="3.0.1" />
    <PackageReference Include="Microsoft.ML.LightGbm" Version="3.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TradingSystem.Core\TradingSystem.Core.fsproj" />
  </ItemGroup>

</Project>
