﻿

open System
open System.IO
open TradingSystem.Core
open TradingSystem.Data

// =============================================================================
// COMMAND LINE INTERFACE
// =============================================================================

type Command =
    | ProcessData of inputPath: string * outputPath: string
    | Test
    | Help

let parseArgs (args: string[]) : Command =
    match args with
    | [| "process"; inputPath; outputPath |] ->
        ProcessData(inputPath, outputPath)
    | [| "test" |] ->
        Test
    | _ -> Help

let printHelp () =
    printfn """
🚀 F# Trading System CLI

USAGE:
    dotnet run [COMMAND] [OPTIONS]

COMMANDS:
    process <input> <output>     Process raw tick CSV files to parquet
    test                         Run system tests
    help                         Show this help

EXAMPLES:
    # Process raw CSV tick data
    dotnet run process raw-ticks/ processed-data/

    # Test the system
    dotnet run test
"""

let testCommand () =
    async {
        printfn "🧪 Running F# Trading System Tests..."

        // Test domain types
        printfn "✅ Testing domain types..."
        let timeframe = Minutes 5
        printfn $"   Timeframe: {timeframe} = {timeframe.ToSeconds()} seconds"

        let tick = {
            Instrument = "EURUSD"
            Timestamp = DateTime.Now
            Ask = 1.1234m
            Bid = 1.1230m
            AskVolume = Some 1000m
            BidVolume = Some 1000m
        }
        printfn $"   Sample tick: {tick.Instrument} @ {tick.Ask}/{tick.Bid}"

        // Test indicator calculations
        printfn "✅ Testing technical indicators..."
        let prices = [| 1.1200m; 1.1210m; 1.1205m; 1.1215m; 1.1220m; 1.1225m; 1.1230m; 1.1235m; 1.1240m; 1.1245m |]
        let sma5 = IndicatorEngine.calculateSMA 5 prices
        let ema5 = IndicatorEngine.calculateEMA 5 prices

        printfn $"   SMA(5) last value: {sma5.[sma5.Length - 1]}"
        printfn $"   EMA(5) last value: {ema5.[ema5.Length - 1]}"

        printfn "🎉 All tests passed!"
    }

let processDataCommand inputPath outputPath =
    async {
        printfn $"🚀 Processing data from {inputPath} to {outputPath}"

        // Check if input file/directory exists
        if not (File.Exists(inputPath) || Directory.Exists(inputPath)) then
            printfn $"❌ Error: Input path does not exist: {inputPath}"
            return ()

        // Create processing configuration
        let config = DataProcessor.createConfig inputPath outputPath

        // Process the data
        do! DataProcessor.processTickData config
    }

[<EntryPoint>]
let main args =
    try
        match parseArgs args with
        | ProcessData(inputPath, outputPath) ->
            processDataCommand inputPath outputPath |> Async.RunSynchronously
            0
        | Test ->
            testCommand() |> Async.RunSynchronously
            0
        | Help ->
            printHelp()
            0
    with
    | ex ->
        printfn $"❌ Error: {ex.Message}"
        1
