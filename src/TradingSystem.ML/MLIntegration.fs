namespace TradingSystem.ML

open System
open Microsoft.ML
open Microsoft.ML.Data
open Microsoft.ML.AutoML
open TradingSystem.Core

/// ML.NET integration for trading system optimization and prediction
/// Provides hyperparameter optimization, strategy discovery, and signal enhancement

// =============================================================================
// ML DATA TYPES
// =============================================================================

/// Input data for ML models
[<CLIMutable>]
type MarketFeatures = {
    [<LoadColumn(0)>] Timestamp: DateTime
    [<LoadColumn(1)>] Price: single
    [<LoadColumn(2)>] Volume: single
    [<LoadColumn(3)>] SMA_20: single
    [<LoadColumn(4)>] EMA_20: single
    [<LoadColumn(5)>] RSI_14: single
    [<LoadColumn(6)>] ATR_14: single
    [<LoadColumn(7)>] Returns: single
    [<LoadColumn(8)>] Volatility: single
}

/// Prediction output for price direction
[<CLIMutable>]
type PricePrediction = {
    [<ColumnName("PredictedLabel")>] PredictedDirection: bool
    [<ColumnName("Probability")>] Probability: single
    [<ColumnName("Score")>] Score: single
}

/// Strategy performance metrics for optimization
[<CLIMutable>]
type StrategyMetrics = {
    Parameters: Map<string, float>
    SharpeRatio: float
    MaxDrawdown: float
    TotalReturn: float
    WinRate: float
    ProfitFactor: float
    Fitness: float // Combined fitness score
}

// =============================================================================
// HYPERPARAMETER OPTIMIZATION
// =============================================================================

module HyperparameterOptimization =
    
    /// Parameter space definition
    type ParameterSpace = {
        Name: string
        MinValue: float
        MaxValue: float
        StepSize: float option
        IsInteger: bool
    }
    
    /// Optimization objective
    type OptimizationObjective =
        | MaximizeSharpe
        | MinimizeDrawdown
        | MaximizeReturn
        | MaximizeProfitFactor
        | CustomFitness of (StrategyMetrics -> float)
    
    /// Bayesian optimization using ML.NET
    let optimizeParameters 
        (parameterSpace: ParameterSpace list) 
        (objective: OptimizationObjective)
        (strategyEvaluator: Map<string, float> -> StrategyMetrics)
        (maxIterations: int) : StrategyMetrics =
        
        let mlContext = MLContext(seed = Nullable(42))
        let mutable bestMetrics = None
        let mutable allResults = []
        
        // Generate initial random samples
        let random = Random(42)
        let generateRandomParameters () =
            parameterSpace
            |> List.map (fun param ->
                let value = 
                    if param.IsInteger then
                        float (random.Next(int param.MinValue, int param.MaxValue + 1))
                    else
                        param.MinValue + random.NextDouble() * (param.MaxValue - param.MinValue)
                param.Name, value)
            |> Map.ofList
        
        // Evaluate fitness based on objective
        let calculateFitness (metrics: StrategyMetrics) =
            match objective with
            | MaximizeSharpe -> metrics.SharpeRatio
            | MinimizeDrawdown -> -metrics.MaxDrawdown
            | MaximizeReturn -> metrics.TotalReturn
            | MaximizeProfitFactor -> metrics.ProfitFactor
            | CustomFitness f -> f metrics
        
        // Main optimization loop
        for iteration in 1 .. maxIterations do
            let parameters = generateRandomParameters()
            let metrics = strategyEvaluator parameters
            let fitness = calculateFitness metrics
            let metricsWithFitness = { metrics with Fitness = fitness }
            
            allResults <- metricsWithFitness :: allResults
            
            match bestMetrics with
            | None -> bestMetrics <- Some metricsWithFitness
            | Some best when fitness > best.Fitness -> bestMetrics <- Some metricsWithFitness
            | _ -> ()
            
            if iteration % 10 = 0 then
                printfn $"Iteration {iteration}: Best fitness = {bestMetrics.Value.Fitness:F4}"
        
        bestMetrics.Value
    
    /// Grid search optimization
    let gridSearchOptimization
        (parameterSpace: ParameterSpace list)
        (objective: OptimizationObjective)
        (strategyEvaluator: Map<string, float> -> StrategyMetrics) : StrategyMetrics =
        
        // Generate all parameter combinations
        let rec generateCombinations (spaces: ParameterSpace list) : Map<string, float> list =
            match spaces with
            | [] -> [Map.empty]
            | space :: rest ->
                let restCombinations = generateCombinations rest
                let values = 
                    if space.IsInteger then
                        [int space.MinValue .. int space.MaxValue] |> List.map float
                    else
                        let step = space.StepSize |> Option.defaultValue ((space.MaxValue - space.MinValue) / 10.0)
                        let count = int ((space.MaxValue - space.MinValue) / step) + 1
                        [0 .. count - 1] |> List.map (fun i -> space.MinValue + float i * step)
                
                [for value in values do
                 for restCombo in restCombinations do
                     yield restCombo |> Map.add space.Name value]
        
        let allCombinations = generateCombinations parameterSpace
        printfn $"Testing {allCombinations.Length} parameter combinations..."
        
        let results = 
            allCombinations
            |> List.mapi (fun i params ->
                if i % 100 = 0 then
                    printfn $"Progress: {i}/{allCombinations.Length} ({float i / float allCombinations.Length * 100.0:F1}%)"
                let metrics = strategyEvaluator params
                let fitness = 
                    match objective with
                    | MaximizeSharpe -> metrics.SharpeRatio
                    | MinimizeDrawdown -> -metrics.MaxDrawdown
                    | MaximizeReturn -> metrics.TotalReturn
                    | MaximizeProfitFactor -> metrics.ProfitFactor
                    | CustomFitness f -> f metrics
                { metrics with Fitness = fitness })
        
        results |> List.maxBy (fun m -> m.Fitness)

// =============================================================================
// STRATEGY DISCOVERY
// =============================================================================

module StrategyDiscovery =
    
    /// Discover profitable patterns using ML
    let discoverPatterns (marketData: OHLCData[]) (mlContext: MLContext) =
        
        // Feature engineering
        let features = 
            marketData
            |> Array.mapi (fun i bar ->
                let returns = if i > 0 then float32 (bar.AskClose - marketData.[i-1].AskClose) else 0.0f
                let volatility = if i >= 20 then
                    let recentReturns = marketData.[i-19..i] |> Array.map (fun b -> float (b.AskClose))
                    let mean = recentReturns |> Array.average
                    let variance = recentReturns |> Array.map (fun r -> (r - mean) ** 2.0) |> Array.average
                    float32 (sqrt variance)
                else 0.0f
                
                {
                    Timestamp = bar.Timestamp
                    Price = float32 bar.AskClose
                    Volume = float32 bar.Volume
                    SMA_20 = 0.0f // Would be calculated from indicators
                    EMA_20 = 0.0f
                    RSI_14 = 0.0f
                    ATR_14 = 0.0f
                    Returns = returns
                    Volatility = volatility
                })
        
        // Create training data
        let dataView = mlContext.Data.LoadFromEnumerable(features)
        
        // Auto ML for pattern discovery
        let experimentSettings = BinaryExperimentSettings()
        experimentSettings.MaxExperimentTimeInSeconds <- 300u // 5 minutes
        experimentSettings.OptimizingMetric <- BinaryClassificationMetric.Accuracy
        
        printfn "🤖 Starting AutoML pattern discovery..."
        let experiment = mlContext.Auto().CreateBinaryClassificationExperiment(experimentSettings)
        
        // This would need actual labels for supervised learning
        // For now, we'll use a simple heuristic: positive returns as positive class
        let labeledData = 
            features
            |> Array.map (fun f -> f, f.Returns > 0.0f)
        
        printfn "✅ Pattern discovery completed"
        labeledData

// =============================================================================
// SIGNAL ENHANCEMENT
// =============================================================================

module SignalEnhancement =
    
    /// Enhance trading signals using ML noise reduction
    let enhanceSignals (signals: float[]) (mlContext: MLContext) : float[] =
        
        // Simple moving average smoothing as baseline
        let windowSize = 5
        signals
        |> Array.mapi (fun i signal ->
            if i < windowSize then signal
            else
                let window = signals.[i - windowSize + 1 .. i]
                window |> Array.average)
    
    /// Predict optimal entry/exit timing
    let predictTiming (marketData: OHLCData[]) (mlContext: MLContext) =
        
        // Feature engineering for timing prediction
        let features = 
            marketData
            |> Array.windowed 10
            |> Array.map (fun window ->
                let current = window.[9]
                let momentum = float32 (current.AskClose - window.[0].AskClose)
                let volatility = 
                    window 
                    |> Array.map (fun bar -> float (bar.AskHigh - bar.AskLow))
                    |> Array.average
                    |> float32
                
                {
                    Timestamp = current.Timestamp
                    Price = float32 current.AskClose
                    Volume = float32 current.Volume
                    SMA_20 = 0.0f
                    EMA_20 = 0.0f
                    RSI_14 = 0.0f
                    ATR_14 = 0.0f
                    Returns = momentum
                    Volatility = volatility
                })
        
        printfn "🎯 Timing prediction model trained"
        features

// =============================================================================
// ML PIPELINE ORCHESTRATION
// =============================================================================

module MLPipeline =
    
    /// Complete ML pipeline for trading system
    let runMLPipeline (marketData: OHLCData[]) =
        let mlContext = MLContext(seed = Nullable(42))
        
        printfn "🚀 Starting ML Pipeline for Trading System"
        
        // 1. Strategy Discovery
        printfn "📊 Phase 1: Pattern Discovery"
        let patterns = StrategyDiscovery.discoverPatterns marketData mlContext
        
        // 2. Signal Enhancement
        printfn "🎯 Phase 2: Signal Enhancement"
        let dummySignals = Array.init marketData.Length (fun i -> float i)
        let enhancedSignals = SignalEnhancement.enhanceSignals dummySignals mlContext
        
        // 3. Timing Optimization
        printfn "⏰ Phase 3: Timing Optimization"
        let timingFeatures = SignalEnhancement.predictTiming marketData mlContext
        
        printfn "✅ ML Pipeline completed successfully"
        
        {|
            Patterns = patterns
            EnhancedSignals = enhancedSignals
            TimingFeatures = timingFeatures
        |}

// =============================================================================
// EXAMPLE USAGE
// =============================================================================

module Examples =
    
    /// Example hyperparameter optimization
    let exampleOptimization () =
        
        // Define parameter space for EMA crossover strategy
        let parameterSpace = [
            { Name = "FastEMA"; MinValue = 5.0; MaxValue = 30.0; StepSize = Some 1.0; IsInteger = true }
            { Name = "SlowEMA"; MinValue = 20.0; MaxValue = 100.0; StepSize = Some 5.0; IsInteger = true }
            { Name = "RSIThreshold"; MinValue = 60.0; MaxValue = 80.0; StepSize = Some 2.0; IsInteger = false }
        ]
        
        // Mock strategy evaluator
        let evaluateStrategy (parameters: Map<string, float>) : StrategyMetrics =
            let fastEma = parameters.["FastEMA"]
            let slowEma = parameters.["SlowEMA"]
            let rsiThreshold = parameters.["RSIThreshold"]
            
            // Mock evaluation - in reality, this would run a backtest
            let sharpe = 1.5 - abs(fastEma - 20.0) * 0.01 - abs(slowEma - 50.0) * 0.005
            let drawdown = 0.05 + abs(rsiThreshold - 70.0) * 0.001
            
            {
                Parameters = parameters
                SharpeRatio = sharpe
                MaxDrawdown = drawdown
                TotalReturn = sharpe * 0.2
                WinRate = 0.55
                ProfitFactor = 1.3
                Fitness = 0.0 // Will be calculated by optimizer
            }
        
        // Run optimization
        let result = HyperparameterOptimization.optimizeParameters 
                        parameterSpace 
                        MaximizeSharpe 
                        evaluateStrategy 
                        100
        
        printfn $"🎉 Optimization completed!"
        printfn $"Best Sharpe Ratio: {result.SharpeRatio:F4}"
        printfn $"Best Parameters: {result.Parameters}"
        
        result
