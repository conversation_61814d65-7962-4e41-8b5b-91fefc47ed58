﻿namespace TradingSystem.Core

open System

/// Core domain types for the trading system
/// These types form the foundation of our event-driven architecture

// =============================================================================
// MARKET DATA TYPES
// =============================================================================

/// Represents different timeframes for market data
type Timeframe =
    | Seconds of int
    | Minutes of int
    | Hours of int
    | Days of int
    | Weeks of int
    | Months of int

    member this.ToSeconds() =
        match this with
        | Seconds s -> s
        | Minutes m -> m * 60
        | Hours h -> h * 3600
        | Days d -> d * 86400
        | Weeks w -> w * 604800
        | Months m -> m * 2629746 // Approximate

    override this.ToString() =
        match this with
        | Seconds s -> $"{s}s"
        | Minutes m -> $"{m}m"
        | Hours h -> $"{h}h"
        | Days d -> $"{d}d"
        | Weeks w -> $"{w}w"
        | Months m -> $"{m}M"

/// Raw tick data from market feeds
type TickData = {
    Instrument: string
    Timestamp: DateTime
    Ask: decimal
    Bid: decimal
    AskVolume: decimal option
    BidVolume: decimal option
}

/// OHLC bar data aggregated from ticks
type OHLCData = {
    Instrument: string
    Timestamp: DateTime
    Timeframe: Timeframe
    AskOpen: decimal
    AskHigh: decimal
    AskLow: decimal
    AskClose: decimal
    BidOpen: decimal
    BidHigh: decimal
    BidLow: decimal
    BidClose: decimal
    Volume: decimal
    TickCount: int
}

/// Technical indicator values
type IndicatorValue = {
    Name: string
    Value: decimal
    Timestamp: DateTime
    Instrument: string
}

/// Indicator configuration
type IndicatorConfig =
    | SMA of period: int
    | EMA of period: int
    | RSI of period: int
    | ATR of period: int
    | MACD of fast: int * slow: int * signal: int
    | BollingerBands of period: int * stdDev: float
    | Stochastic of k: int * d: int
    | ADX of period: int
    | Custom of name: string * calculator: (OHLCData[] -> decimal[])

// =============================================================================
// TRADING TYPES
// =============================================================================

/// Order types supported by the system
type OrderType =
    | Market
    | Limit of decimal
    | Stop of decimal
    | StopLimit of decimal * decimal

/// Order direction
type OrderDirection =
    | Long
    | Short

/// Order status
type OrderStatus =
    | Pending
    | PartiallyFilled of decimal
    | Filled
    | Cancelled
    | Rejected of string

/// Unique identifier for orders
type OrderId = OrderId of Guid

/// Trading order
type Order = {
    Id: OrderId
    Instrument: string
    Direction: OrderDirection
    OrderType: OrderType
    Quantity: decimal
    Status: OrderStatus
    CreatedAt: DateTime
    FilledAt: DateTime option
    FilledPrice: decimal option
    StopLoss: decimal option
    TakeProfit: decimal option
    TrailingStop: decimal option
}

/// Position in the market
type Position = {
    Instrument: string
    Direction: OrderDirection
    Quantity: decimal
    EntryPrice: decimal
    EntryTime: DateTime
    CurrentPrice: decimal
    UnrealizedPnL: decimal
    StopLoss: decimal option
    TakeProfit: decimal option
    TrailingStop: decimal option
}

/// Execution details for filled orders
type Execution = {
    OrderId: OrderId
    Instrument: string
    Direction: OrderDirection
    Quantity: decimal
    Price: decimal
    Timestamp: DateTime
    Commission: decimal
}

// =============================================================================
// RISK MANAGEMENT TYPES
// =============================================================================

/// Risk levels for alerts and limits
type RiskLevel =
    | Low
    | Medium
    | High
    | Critical

/// Risk metrics for positions and portfolio
type RiskMetrics = {
    MaxDrawdown: decimal
    CurrentDrawdown: decimal
    VaR95: decimal // Value at Risk 95%
    Sharpe: decimal option
    MaxRiskPerTrade: decimal
    TotalExposure: decimal
}

/// Risk alert
type RiskAlert = {
    Level: RiskLevel
    Message: string
    Timestamp: DateTime
    Instrument: string option
}

// =============================================================================
// EVENT TYPES
// =============================================================================

/// Core events that drive the trading system
type MarketEvent =
    | Tick of TickData
    | Bar of OHLCData
    | IndicatorUpdate of IndicatorValue
    | OrderFilled of Execution
    | PositionOpened of Position
    | PositionClosed of Position * decimal // Position and realized PnL
    | RiskAlert of RiskAlert
    | SystemEvent of string * DateTime

/// Strategy events
type StrategyEvent =
    | EntrySignal of OrderDirection * string // Direction and reason
    | ExitSignal of string // Reason for exit
    | ParameterUpdate of string * obj // Parameter name and new value
    | StateChange of string * obj // State variable and new value
