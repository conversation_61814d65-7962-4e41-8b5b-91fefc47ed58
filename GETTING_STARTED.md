# Getting Started with F# Trading System

## 🚀 Quick Setup (5 minutes)

### Prerequisites
Make sure you have .NET 6.0+ installed on your Linux system:

```bash
# Check if .NET is installed
dotnet --version

# If not installed, install it:
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y dotnet-sdk-6.0
```

### Setup the Project
```bash
cd f-sharp
./scripts/setup.sh    # Creates project structure and installs dependencies
./scripts/build.sh    # Builds all projects and runs tests
```

## 🎯 Your First Strategy

### 1. Understanding the Data
Your existing parquet files contain millions of tick and OHLC records:
```bash
# Check what data we have
ls -la data/parquet/strategy_data/
```

### 2. Run the Example Strategy
```bash
# Run the EMA crossover example
dotnet run --project src/TradingSystem.CLI -- backtest \
  --strategy examples/ema-crossover.fs \
  --data data/parquet/strategy_data/strategy_data_EURUSD_2025-01-01_to_2025-07-18_1m.parquet \
  --timeframe 1m \
  --start 2025-01-01 \
  --end 2025-07-18
```

### 3. Create Your Own Strategy
```fsharp
// Create: examples/my-strategy.fs
open TradingSystem.Core
open TradingSystem.Strategy

let myStrategy = strategy {
    name "My First Strategy"
    timeframe (Minutes 5)
    
    indicators [
        ema 20 "fast_ema"
        rsi 14 "momentum"
    ]
    
    entry [
        long_when (price > indicator "fast_ema")
        and_when (indicator "momentum" < 70.0)
        with_stop_loss (pips 20.0)
        with_take_profit (pips 40.0)
    ]
    
    exit [
        when (price < indicator "fast_ema")
    ]
}
```

## 📊 Key Features You Can Use Right Away

### 1. **High-Performance Backtesting**
Process millions of events per second:
```bash
dotnet run backtest --strategy my-strategy.fs --data large-dataset.parquet
```

### 2. **Parameter Optimization**
Find the best parameters automatically:
```bash
dotnet run optimize \
  --strategy examples/ema-crossover.fs \
  --objective sharpe-ratio \
  --iterations 1000
```

### 3. **Live Trading Simulation**
Connect to AMQP for real-time testing:
```bash
dotnet run live \
  --strategy optimized-strategy.fs \
  --amqp amqp://localhost:5672 \
  --mode simulation
```

### 4. **Machine Learning Integration**
Discover new strategies automatically:
```bash
dotnet run discover \
  --data historical-data.parquet \
  --target-return 0.15 \
  --max-drawdown 0.10
```

## 🎨 Strategy Development Workflow

### 1. **Design Phase**
- Define your trading hypothesis
- Identify required indicators
- Sketch entry/exit rules
- Set risk parameters

### 2. **Implementation Phase**
```fsharp
// Use the F# DSL for clean, readable strategies
let myStrategy = strategy {
    // Your strategy logic here
}
```

### 3. **Testing Phase**
```bash
# Quick backtest
dotnet run backtest --strategy my-strategy.fs --data sample-data.parquet

# Full optimization
dotnet run optimize --strategy my-strategy.fs --data full-dataset.parquet
```

### 4. **Deployment Phase**
```bash
# Deploy to live demo account
dotnet run live --strategy optimized-strategy.fs --account demo-12345
```

## 🔧 Advanced Features

### 1. **Multi-Timeframe Analysis**
```fsharp
let multiTimeframeStrategy = strategy {
    timeframes [ Minutes 5; Minutes 15; Hours 1 ]
    
    entry [
        // Use different timeframes in your logic
        long_when (trend_on (Hours 1) = Bullish)
        and_when (pullback_on (Minutes 5))
    ]
}
```

### 2. **Portfolio Management**
```fsharp
let portfolioStrategy = strategy {
    instruments [ "EURUSD"; "GBPUSD"; "USDJPY" ]
    max_concurrent_trades 3
    max_risk_per_trade 0.02 // 2%
    
    // Strategy logic applies to all instruments
}
```

### 3. **Machine Learning Integration**
```fsharp
let mlEnhancedStrategy = strategy {
    // Use ML models for entry/exit decisions
    entry [
        long_when (ml_model "trend_predictor" > 0.7)
        and_when (traditional_signal)
    ]
}
```

## 🚨 Common Issues & Solutions

### Issue: "dotnet command not found"
**Solution:** Install .NET SDK as shown in prerequisites

### Issue: "Permission denied" on scripts
**Solution:** 
```bash
chmod +x scripts/*.sh
```

### Issue: "Cannot find parquet data"
**Solution:** Check that the symlink was created correctly:
```bash
ls -la data/parquet/strategy_data
```

### Issue: Build errors
**Solution:** Clean and rebuild:
```bash
./scripts/build.sh --debug --verbose
```

## 📚 Next Steps

1. **Read the Documentation**
   - [Architecture Overview](docs/architecture/README.md)
   - [Strategy Development Guide](docs/strategies/README.md)
   - [API Reference](docs/api/README.md)

2. **Explore Examples**
   - `examples/ema-crossover.fs` - Basic trend following
   - `examples/mean-reversion.fs` - Counter-trend strategy
   - `examples/multi-timeframe.fs` - Complex analysis

3. **Join the Community**
   - Share your strategies
   - Get help with development
   - Contribute improvements

## 🎯 Success Metrics

After following this guide, you should be able to:
- ✅ Create and backtest your first strategy in < 10 minutes
- ✅ Process millions of historical records efficiently
- ✅ Optimize strategy parameters automatically
- ✅ Connect to live demo accounts for testing
- ✅ Use machine learning for strategy enhancement

**Happy Trading!** 📈🚀
