# VS Code Setup for F# Trading System

## 🔧 **Required Extensions**

### **Essential F# Extensions:**
```bash
# Install via VS Code Extensions or command line:
code --install-extension Ionide.Ionide-fsharp
code --install-extension ms-dotnettools.csharp
code --install-extension ms-vscode.vscode-json
```

### **Recommended Extensions:**
```bash
# Additional helpful extensions:
code --install-extension ms-vscode.powershell
code --install-extension ms-python.python
code --install-extension redhat.vscode-yaml
code --install-extension ms-vscode.cmake-tools
code --install-extension GitHub.copilot  # If you have access
```

## ⚙️ **VS Code Configuration**

### **Workspace Settings (`.vscode/settings.json`):**
```json
{
    "FSharp.inlayHints.enabled": true,
    "FSharp.inlayHints.typeAnnotations": true,
    "FSharp.inlayHints.parameterNames": true,
    "FSharp.codeLenses.signature.enabled": true,
    "FSharp.codeLenses.references.enabled": true,
    "FSharp.showExplorerOnStartup": true,
    "FSharp.enableTreeView": true,
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    "omnisharp.enableEditorConfigSupport": true,
    "files.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true
    },
    "search.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/node_modules": true,
        "**/.vs": true
    },
    "files.watcherExclude": {
        "**/bin/**": true,
        "**/obj/**": true,
        "**/.vs/**": true
    }
}
```

### **Launch Configuration (`.vscode/launch.json`):**
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch CLI",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/TradingSystem.CLI/bin/Debug/net6.0/TradingSystem.CLI.dll",
            "args": ["test"],
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Process Data",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/TradingSystem.CLI/bin/Debug/net6.0/TradingSystem.CLI.dll",
            "args": ["process", "data/csv/sample.csv", "data/output"],
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Run Tests",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "dotnet",
            "args": ["test"],
            "cwd": "${workspaceFolder}",
            "console": "internalConsole",
            "stopAtEntry": false
        }
    ]
}
```

### **Tasks Configuration (`.vscode/tasks.json`):**
```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "command": "dotnet",
            "type": "process",
            "args": ["build"],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "test",
            "command": "dotnet",
            "type": "process",
            "args": ["test"],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "clean",
            "command": "dotnet",
            "type": "process",
            "args": ["clean"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "restore",
            "command": "dotnet",
            "type": "process",
            "args": ["restore"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "format",
            "command": "dotnet",
            "type": "process",
            "args": ["fantomas", "src/", "tests/"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        }
    ]
}
```

## 🚀 **Quick Start in VS Code**

### **1. Open Project:**
```bash
# Open the f-sharp directory in VS Code
code f-sharp/
```

### **2. Initial Setup:**
1. **Trust the workspace** when prompted
2. **Install recommended extensions** (VS Code will prompt)
3. **Wait for Ionide to load** (F# language server)
4. **Run initial build**: `Ctrl+Shift+P` → "Tasks: Run Task" → "build"

### **3. Verify Setup:**
```bash
# In VS Code terminal (Ctrl+`):
./scripts/build.sh
dotnet run --project src/TradingSystem.CLI test
```

## 🔍 **Key VS Code Features for F#**

### **IntelliSense & Navigation:**
- **Auto-completion**: `Ctrl+Space`
- **Go to definition**: `F12`
- **Find all references**: `Shift+F12`
- **Peek definition**: `Alt+F12`
- **Symbol search**: `Ctrl+T`

### **Debugging:**
- **Set breakpoints**: Click in gutter or `F9`
- **Start debugging**: `F5`
- **Step over**: `F10`
- **Step into**: `F11`
- **Continue**: `F5`

### **F# Specific Features:**
- **F# Interactive (FSI)**: `Ctrl+Shift+P` → "F#: Start FSI"
- **Send to FSI**: `Alt+Enter`
- **Show type info**: Hover over code
- **Signature help**: `Ctrl+Shift+Space`

### **Code Formatting:**
- **Format document**: `Shift+Alt+F`
- **Format selection**: `Ctrl+K, Ctrl+F`
- **Auto-format on save**: Enable in settings

## 📁 **Recommended Folder Structure in VS Code**

### **Explorer View:**
```
f-sharp/
├── 📁 src/
│   ├── 📁 TradingSystem.Core/      ← Domain types
│   ├── 📁 TradingSystem.Data/      ← Data processing
│   ├── 📁 TradingSystem.Strategy/  ← Strategy framework
│   ├── 📁 TradingSystem.Execution/ ← Backtesting
│   ├── 📁 TradingSystem.ML/        ← ML integration
│   └── 📁 TradingSystem.CLI/       ← Command line
├── 📁 tests/
├── 📁 examples/
├── 📁 data/
├── 📁 docs/
├── 📄 TradingSystem.sln            ← Solution file
├── 📄 README.md
└── 📄 QUICK_REFERENCE.md
```

## ⌨️ **Useful Keyboard Shortcuts**

### **General:**
- `Ctrl+Shift+P`: Command palette
- `Ctrl+P`: Quick file open
- `Ctrl+Shift+E`: Explorer
- `Ctrl+Shift+F`: Search across files
- `Ctrl+Shift+G`: Source control
- `Ctrl+`` `: Terminal

### **F# Specific:**
- `Alt+Enter`: Send to F# Interactive
- `Ctrl+.`: Quick fix/refactor
- `F2`: Rename symbol
- `Ctrl+Shift+O`: Go to symbol in file
- `Ctrl+T`: Go to symbol in workspace

### **Debugging:**
- `F5`: Start/continue debugging
- `F9`: Toggle breakpoint
- `F10`: Step over
- `F11`: Step into
- `Shift+F11`: Step out

## 🔧 **Troubleshooting VS Code Issues**

### **F# Language Server Not Working:**
1. **Restart Ionide**: `Ctrl+Shift+P` → "F#: Restart Language Server"
2. **Check .NET SDK**: `dotnet --version` in terminal
3. **Reload window**: `Ctrl+Shift+P` → "Developer: Reload Window"
4. **Clear cache**: Delete `.ionide/` folder and restart

### **IntelliSense Not Working:**
1. **Build project**: `Ctrl+Shift+P` → "Tasks: Run Task" → "build"
2. **Restore packages**: `dotnet restore`
3. **Check project references**: Verify `.fsproj` files
4. **Restart VS Code**

### **Debugging Issues:**
1. **Build in Debug mode**: `dotnet build --configuration Debug`
2. **Check launch.json**: Verify paths and arguments
3. **Set breakpoints**: Ensure they're in executable code
4. **Check console output**: Look for error messages

## 📊 **Performance Monitoring in VS Code**

### **Terminal Commands:**
```bash
# Monitor build performance
time dotnet build

# Watch for file changes
dotnet watch --project src/TradingSystem.CLI run

# Profile memory usage
dotnet-counters monitor --process-id <pid>

# Trace performance
dotnet-trace collect --process-id <pid>
```

### **Extensions for Performance:**
- **Resource Monitor**: Monitor CPU/memory usage
- **GitLens**: Git integration and history
- **Error Lens**: Inline error display
- **Bracket Pair Colorizer**: Better code readability

## 🎯 **Development Workflow**

### **Daily Workflow:**
1. **Open VS Code**: `code f-sharp/`
2. **Pull latest changes**: `git pull`
3. **Build project**: `Ctrl+Shift+P` → "Tasks: Run Task" → "build"
4. **Run tests**: `Ctrl+Shift+P` → "Tasks: Run Task" → "test"
5. **Start development**: Edit files with full IntelliSense
6. **Debug as needed**: Set breakpoints and press `F5`
7. **Format code**: `Shift+Alt+F` before committing
8. **Commit changes**: Use Source Control panel

### **Testing Workflow:**
1. **Write tests**: In `tests/` directory
2. **Run specific test**: `dotnet test --filter TestName`
3. **Debug test**: Set breakpoint and use "Run Tests" launch config
4. **Check coverage**: Use coverage tools if needed

**VS Code is perfectly set up for F# development with excellent tooling support!** 🚀💻
